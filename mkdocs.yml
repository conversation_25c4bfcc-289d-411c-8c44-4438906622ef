site_name: YAICLI
site_description: An advanced CLI tool for interacting with AI models
site_url: https://belingud.github.io/yaicli
repo_url: https://github.com/belingud/yaicli
repo_name: belingud/yaicli

theme:
  name: material
  palette:
    - scheme: default
      primary: indigo
      accent: indigo
      toggle:
        icon: material/toggle-switch
        name: Switch to dark mode
    - scheme: slate
      primary: indigo
      accent: indigo
      toggle:
        icon: material/toggle-switch-off-outline
        name: Switch to light mode
  features:
    - navigation.instant
    - navigation.tracking
    - navigation.expand
    - navigation.indexes
    - content.code.copy
    - content.code.annotate
  logo: assets/artwork/logo.png
  favicon: assets/artwork/logo.png

nav:
  - Home: index.md
  - Getting Started: getting-started.md
  - Installation: install.md
  - Usage:
      - Command Overview: usage/commands.md
      - Configuration Guide: usage/configuration.md
      - Mode Switching (Chat / Execute): usage/modes.md
      - CLI Shortcuts & Hotkeys: usage/cli.md
      - Basic Usage: usage/basic.md
      - Interactive Mode: usage/interactive.md
      - Advanced Usage: usage/advanced.md
  - Providers:
      - Overview & Feature Matrix: providers/overview.md
      - OpenAI: providers/openai.md
      - Anthropic (Claude): providers/anthropic.md
      - Gemini: providers/gemini.md
      - Cohere: providers/cohere.md
      - Ollama: providers/ollama.md
      - Mistral: providers/mistral.md
      - DeepSeek: providers/deepseek.md
      - Groq: providers/groq.md
      - Together: providers/together.md
      - VertexAI: providers/vertexai.md
      - OpenRouter: providers/openrouter.md
      - HuggingFace: providers/huggingface.md
      - XAI (Grok): providers/xai.md
      - Yi (01.AI): providers/yi.md
      - Cerebras: providers/cerebras.md
      - Doubao: providers/doubao.md
      - NVIDIA NIM: providers/nvidia.md
      - SambaNova: providers/sambanova.md
      - Moonshot: providers/moonshot.md
      - ModelScope: providers/modelscope.md
      - ChatGLM: providers/chatglm.md
      - Bailian: providers/bailian.md
      - Minimax: providers/minimax.md
  - Advanced:
      - Prompt Template System: advanced/prompts.md
      - Conversation & History: advanced/history.md
      - Debugging & Logs: advanced/debugging.md
  - Developer Guide:
      - Architecture Overview: dev/architecture.md
      - Add a New Provider: dev/add-provider.md
      - CLI Debugging: dev/debug-cli.md
      - Contributing: dev/contributing.md
  - FAQ & Troubleshooting: faq.md
  - Changelog: changelog.md

markdown_extensions:
  - admonition
  - codehilite
  - toc:
      permalink: true
  - pymdownx.superfences
  - pymdownx.highlight
  - pymdownx.inlinehilite
  - pymdownx.details
  - pymdownx.emoji
  - pymdownx.tabbed:
      alternate_style: true

plugins:
  - search
  - mkdocstrings
  # - git-revision-date-localized:
  #     fallback_to_build_date: true

extra:
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/belingud/yaicli
