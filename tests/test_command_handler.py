from unittest.mock import MagicMock, patch

import pytest

from yaicli.command_handler import CommandHandler
from yaicli.const import (
    CHAT_MODE,
    EXEC_MODE,
    DefaultRoleNames,
)


@pytest.fixture
def mock_cli():
    """Create a mock CLI for testing CommandHandler."""
    cli = MagicMock()
    cli.console = MagicMock()
    cli.chat = MagicMock()
    cli.chat.history = []
    cli.chat.title = "Test Chat"
    cli.current_mode = CHAT_MODE
    cli.init_role = DefaultRoleNames.DEFAULT
    return cli


@pytest.fixture
def command_handler(mock_cli):
    """Create a CommandHandler instance with mock CLI for testing."""
    return CommandHandler(mock_cli)


class TestCommandHandler:
    """Test the CommandHandler class."""

    def test_handle_shell_command(self, command_handler):
        """Test handling shell commands with ! prefix."""
        with patch("subprocess.call") as mock_subprocess:
            # Test with a valid command
            result = command_handler.handle_shell_command("ls -la")
            assert result is True
            mock_subprocess.assert_called_once_with("ls -la", shell=True)

            # Test with empty command
            mock_subprocess.reset_mock()
            result = command_handler.handle_shell_command("")
            assert result is True
            mock_subprocess.assert_not_called()

            # Test with exception
            mock_subprocess.side_effect = Exception("Command failed")
            result = command_handler.handle_shell_command("bad-command")
            assert result is True  # Should return True to continue REPL loop
            command_handler.cli.console.print.assert_any_call("Failed to execute command: Command failed", style="red")

    def test_handle_exclamation_commands(self, command_handler):
        """Test handling commands with exclamation mark prefixes."""
        with patch.object(command_handler, "handle_shell_command") as mock_handle:
            mock_handle.return_value = True

            # Test half-width exclamation mark
            result = command_handler.handle_command("!ls -la")
            assert result is True
            mock_handle.assert_called_once_with("ls -la")

            # Test full-width exclamation mark
            mock_handle.reset_mock()
            result = command_handler.handle_command("！pwd")
            assert result is True
            mock_handle.assert_called_once_with("pwd")

    def test_handle_help_command(self, command_handler):
        """Test handling help command."""
        with patch.object(command_handler, "handle_help", return_value=True):
            result = command_handler.handle_command("/help")
            assert result is True
            command_handler.cli.print_help.assert_called_once()

    def test_handle_exit_command(self, command_handler):
        """Test handling exit command."""
        result = command_handler.handle_command("/exit")
        assert result is False  # Should return False to exit REPL loop

    def test_handle_clear_command(self, command_handler):
        """Test handling clear command."""
        # Set up a mock history
        command_handler.cli.chat.history = ["message1", "message2"]
        command_handler.cli.current_mode = CHAT_MODE

        result = command_handler.handle_command("/clear")
        assert result is True
        assert command_handler.cli.chat.history == []
        command_handler.cli.console.print.assert_any_call("Chat history cleared", style="bold yellow")

        # Test in non-chat mode - should still return True but not clear
        command_handler.cli.current_mode = EXEC_MODE
        command_handler.cli.chat.history = ["message1", "message2"]
        result = command_handler.handle_command("/clear")
        assert result is True
        assert command_handler.cli.chat.history == ["message1", "message2"]

    def test_handle_history_command(self, command_handler):
        """Test handling history command."""
        # Test with empty history
        command_handler.cli.chat.history = []
        result = command_handler.handle_command("/his")
        assert result is True
        command_handler.cli.console.print.assert_any_call("History is empty.", style="yellow")

        # Test with history content - just verify method behavior as the actual
        # display logic is complex
        command_handler.cli.console.print.reset_mock()
        command_handler.cli.chat.history = [
            MagicMock(role="user", content="test message"),
            MagicMock(role="assistant", content="test response"),
        ]
        result = command_handler.handle_command("/his")
        assert result is True
        # Should print chat history header
        command_handler.cli.console.print.assert_any_call("Chat History:", style="bold underline")

    def test_handle_save_command(self, command_handler):
        """Test handling save command."""
        # Test with title
        result = command_handler.handle_command("/save New Title")
        assert result is True
        command_handler.cli._save_chat.assert_called_once_with("New Title")

        # Test without title
        command_handler.cli._save_chat.reset_mock()
        result = command_handler.handle_command("/save")
        assert result is True
        command_handler.cli._save_chat.assert_called_once_with(command_handler.cli.chat.title)

    def test_handle_load_command(self, command_handler):
        """Test handling load command."""
        # Test with valid index
        result = command_handler.handle_command("/load 1")
        assert result is True
        command_handler.cli._load_chat_by_index.assert_called_once_with(index="1")

        # Test without index - should show usage message
        command_handler.cli._load_chat_by_index.reset_mock()
        command_handler.cli.console.print.reset_mock()
        result = command_handler.handle_command("/load")
        assert result is True
        command_handler.cli.console.print.assert_any_call("Usage: /load <index>", style="yellow")
        command_handler.cli._list_chats.assert_called_once()

    def test_handle_delete_command(self, command_handler):
        """Test handling delete command."""
        # Test with valid index
        result = command_handler.handle_command("/delete 1")
        assert result is True
        command_handler.cli._delete_chat_by_index.assert_called_once_with(index="1")

        # Test without index - should show usage message
        command_handler.cli._delete_chat_by_index.reset_mock()
        command_handler.cli.console.print.reset_mock()
        result = command_handler.handle_command("/delete")
        assert result is True
        command_handler.cli.console.print.assert_any_call("Usage: /del <index>", style="yellow")
        command_handler.cli._list_chats.assert_called_once()

    def test_handle_list_command(self, command_handler):
        """Test handling list command."""
        with patch.object(command_handler, "handle_list", return_value=True):
            result = command_handler.handle_command("/chats")
            assert result is True
            command_handler.cli._list_chats.assert_called_once()

    def test_handle_mode_command(self, command_handler):
        """Test handling mode command."""
        # Test switching to exec mode
        command_handler.cli.current_mode = CHAT_MODE
        result = command_handler.handle_command("/mode exec")
        assert result is True
        command_handler.cli.set_role.assert_called_once_with(DefaultRoleNames.SHELL)
        assert command_handler.cli.current_mode == EXEC_MODE

        # Test with same mode
        command_handler.cli.console.print.reset_mock()
        command_handler.cli.set_role.reset_mock()
        result = command_handler.handle_command("/mode exec")
        assert result is True
        command_handler.cli.console.print.assert_any_call("Already in exec mode.", style="yellow")
        command_handler.cli.set_role.assert_not_called()

        # Test with invalid mode
        command_handler.cli.console.print.reset_mock()
        result = command_handler.handle_command("/mode invalid")
        assert result is True
        command_handler.cli.console.print.assert_any_call("Usage: /mode chat|exec", style="yellow")

    def test_handle_non_special_command(self, command_handler):
        """Test handling non-special command."""
        result = command_handler.handle_command("hello world")
        assert result == "hello world"  # Should return the original input
