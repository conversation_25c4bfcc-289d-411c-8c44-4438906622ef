output_dir: "./repo-serialized"

# Add patterns to ignore (in addition to .gitignore)
ignore_patterns:
  - "repo-serialized/**"
  - ".vscode"
  - "LICENSE"
  - "pyrightconfig.json"
  - "uv.lock"
  - "var/"
  - "artwork/"
  - "*.cast"
  - "*.tape"
  - "dist/"
  - "tests/*"
  - "yek.yaml"
  - "__pycache__"
  - "cliff.toml"
  - "CHANGELOG.md"
  - ".env"
  - "README.md"
  - "Justfile"
  - "pyproject.toml"

# Configure Git-based priority boost (optional)
git_boost_max: 100  # Maximum score boost based on Git history (default: 100)

# Define priority rules for processing order
# Higher scores are processed first
priority_rules:
  - score: 100
    pattern: "^src/lib/"
  - score: 90
    pattern: "^src/"
  - score: 80
    pattern: "^docs/"

# Add additional binary file extensions to ignore
# These extend the built-in list (.jpg, .png, .exe, etc.)
binary_extensions:
  - ".blend"  # Blender files
  - ".fbx"    # 3D model files
  - ".max"    # 3ds Max files
  - ".psd"    # Photoshop files
