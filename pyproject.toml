[project]
name = "yaicli"
version = "0.7.17"
description = "A simple CLI tool to interact with LLM"
authors = [{ name = "belingud", email = "<EMAIL>" }]
readme = "README.md"
requires-python = ">=3.10"
license = { file = "LICENSE" }
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
]
keywords = [
    "cli",
    "llm",
    "ai",
    "chatgpt",
    "gpt",
    "llms",
    "terminal",
    "interactive",
    "command-line",
    "ai-assistant",
    "language-model",
    "text-generation",
    "conversation",
    "prompt",
    "completion",
    "console-application",
    "shell-integration",
    "nlp",
    "inference",
    "ai-chat",
    "python-tool",
    "terminal-interface",
    "ai-interaction",
    "openai",
    "claude",
    "gemini",
    "mistral",
    "anthropic",
    "groq",
    "cohere",
    "huggingface",
    "chatglm",
    "sambanova",
    "siliconflow",
    "xai",
    "vertexai",
    "deepseek",
    "modelscope",
    "ollama",
]
dependencies = [
    "click>=8.1.8",
    "distro>=1.9.0",
    "fastmcp>=2.10.1",
    "httpx>=0.28.1",
    "instructor>=1.7.9",
    "json-repair>=0.44.1",
    "openai>=1.76.0",
    "prompt-toolkit>=3.0.50",
    "rich>=13.9.4",
    "socksio>=1.0.0",
    "typer>=0.16.0",
]
[project.urls]
Homepage = "https://github.com/belingud/yaicli"
Repository = "https://github.com/belingud/yaicli"
Documentation = "https://github.com/belingud/yaicli"

[project.scripts]
ai = "yaicli.entry:app"
yaicli = "yaicli.entry:app"

[project.optional-dependencies]
all = [
    "volcengine-python-sdk>=3.0.15",
    "ollama>=0.5.1",
    "cohere>=5.15.0",
    "google-genai>=1.20.0",
    "huggingface-hub>=0.33.0",
    "mistralai>=1.8.2",
    "anthropic[bedrock,vertex]>=0.57.1",
    "cerebras-cloud-sdk>=1.35.0",
]
doubao = ["volcengine-python-sdk>=3.0.15"]
ollama = ["ollama>=0.5.1"]
cohere = ["cohere>=5.15.0"]
gemini = ["google-genai>=1.20.0"]
huggingface = ["huggingface-hub>=0.33.0"]
mistral = ["mistralai>=1.8.2"]
anthropic = ["anthropic[bedrock,vertex]>=0.57.1"]
cerebras = ["cerebras-cloud-sdk>=1.35.0"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_functions = ["test_*"]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
    "ignore::UserWarning",
    "ignore::pydantic.PydanticDeprecatedSince20",
    "ignore:.*There is no current event loop.*:DeprecationWarning",
]

[tool.uv]
resolution = "highest"

[dependency-groups]
dev = [
    "bump2version>=1.0.1",
    "pytest>=8.3.5",
    "pytest-cov>=6.1.1",
    "ruff>=0.11.2",
    "tox>=4.27.0",
    "tox-uv>=1.26.1",
]
docs = ["mkdocs>=1.6.1", "mkdocs-material>=9.6.15", "mkdocstrings>=0.29.1"]

[tool.isort]
profile = "black"

[tool.ruff]
line-length = 120
fix = true

[tool.ruff.lint]
select = ["F"]
fixable = ["F401"]

[build-system]
requires = ["hatchling>=1.18.0"]
build-backend = "hatchling.build"

[tool.hatch.build]
exclude = ["test_*.py", "tests/*", ".gitignore"]
include = ["yaicli", "pyproject.toml"]
